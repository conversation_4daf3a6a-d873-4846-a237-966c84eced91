#!/usr/bin/env python3
"""
Example script demonstrating how to insert logs into a Braintrust project using the SDK.

This script shows different ways to log data:
1. Simple log with input/output
2. Log with scores and metadata
3. Log with spans for structured logging
4. Batch logging multiple events

Usage:
    python insert_log_example.py

Make sure to set your BRAINTRUST_API_KEY environment variable or pass it as a parameter.
"""

import os
import time
import braintrust
from typing import Any, Dict


def simple_log_example():
    """Example of basic logging to a project."""
    print("=== Simple Log Example ===")
    
    # Initialize logger for a project (will create project if it doesn't exist)
    logger = braintrust.init_logger(project="pedro-repro4667")
    
    # Log a simple event
    log_id = logger.log(
        input="What is the capital of France?",
        output="The capital of France is Paris.",
        scores={"accuracy": 1.0},
        metadata={"model": "gpt-3.5-turbo", "user_id": "user123"}
    )
    
    print(f"Logged event with ID: {log_id}")
    
    # Flush to ensure the log is sent to the server
    logger.flush()
    print("Log flushed to server")


def structured_logging_with_spans():
    """Example of structured logging using spans."""
    print("\n=== Structured Logging with Spans ===")
    
    logger = braintrust.init_logger(project="pedro-repro4667")
    
    # Create a parent span for a task
    with logger.start_span(name="question_answering_task") as task_span:
        task_span.log(
            input="What are the benefits of renewable energy?",
            metadata={"task_type": "qa", "difficulty": "medium"}
        )
        
        # Create child spans for sub-tasks
        with task_span.start_span(name="information_retrieval") as retrieval_span:
            retrieval_span.log(
                input="renewable energy benefits",
                output=["reduced carbon emissions", "sustainable", "cost-effective"],
                scores={"relevance": 0.9}
            )
        
        with task_span.start_span(name="answer_generation") as generation_span:
            generation_span.log(
                input=["reduced carbon emissions", "sustainable", "cost-effective"],
                output="Renewable energy offers several benefits including reduced carbon emissions, long-term sustainability, and cost-effectiveness over time.",
                scores={"coherence": 0.95, "completeness": 0.8}
            )
        
        # Log the final result on the parent span
        task_span.log(
            output="Renewable energy offers several benefits including reduced carbon emissions, long-term sustainability, and cost-effectiveness over time.",
            expected="A comprehensive answer about renewable energy benefits",
            scores={"overall_quality": 0.9}
        )
    
    logger.flush()
    print("Structured logs with spans flushed to server")


def batch_logging_example():
    """Example of logging multiple events in batch."""
    print("\n=== Batch Logging Example ===")
    
    logger = braintrust.init_logger(project="pedro-repro4667")
    
    # Sample data to log
    qa_pairs = [
        {
            "input": "What is machine learning?",
            "output": "Machine learning is a subset of AI that enables computers to learn from data.",
            "expected": "A clear definition of machine learning",
            "scores": {"accuracy": 0.9, "clarity": 0.8}
        },
        {
            "input": "How does neural network work?",
            "output": "Neural networks process information through interconnected nodes that mimic brain neurons.",
            "expected": "An explanation of neural network functioning",
            "scores": {"accuracy": 0.85, "clarity": 0.9}
        },
        {
            "input": "What is deep learning?",
            "output": "Deep learning uses multi-layered neural networks to learn complex patterns in data.",
            "expected": "A definition of deep learning",
            "scores": {"accuracy": 0.95, "clarity": 0.85}
        }
    ]
    
    # Log each event
    log_ids = []
    for i, qa in enumerate(qa_pairs):
        log_id = logger.log(
            input=qa["input"],
            output=qa["output"],
            expected=qa["expected"],
            scores=qa["scores"],
            metadata={
                "batch_id": "batch_001",
                "sequence_number": i + 1,
                "timestamp": time.time()
            },
            tags=["batch_processing", "qa_evaluation"]
        )
        log_ids.append(log_id)
        print(f"Logged QA pair {i+1} with ID: {log_id}")
    
    logger.flush()
    print(f"Batch of {len(log_ids)} logs flushed to server")


def advanced_logging_example():
    """Example with error handling and custom metadata."""
    print("\n=== Advanced Logging Example ===")
    
    logger = braintrust.init_logger(
        project="pedro-repro4667",
        async_flush=True  # Enable async flushing (default)
    )
    
    try:
        # Simulate an AI task that might fail
        with logger.start_span(name="ai_task_with_error_handling") as span:
            span.log(
                input="Translate 'Hello world' to Spanish",
                metadata={"model": "translation-model-v1", "language_pair": "en-es"}
            )
            
            # Simulate some processing time
            time.sleep(0.1)
            
            # Simulate an error scenario
            try:
                # This would be your actual AI model call
                result = "Hola mundo"  # Successful translation
                
                span.log(
                    output=result,
                    scores={"translation_quality": 0.95, "fluency": 0.9},
                    metrics={"processing_time_ms": 100, "tokens_processed": 2}
                )
                
            except Exception as e:
                # Log the error
                span.log(
                    error=str(e),
                    metadata={"error_type": type(e).__name__},
                    scores={"success": 0.0}
                )
                raise
    
    except Exception as e:
        print(f"Handled error: {e}")
    
    logger.flush()
    print("Advanced logging example completed")


def main():
    """Run all logging examples."""
    print("Braintrust SDK Log Insertion Examples")
    print("=" * 40)
    
    # Check if API key is available
    api_key = os.getenv("BRAINTRUST_API_KEY")
    if not api_key:
        print("Warning: BRAINTRUST_API_KEY environment variable not set.")
        print("You may be prompted to login or the script may fail.")
        print()
    
    try:
        # Run all examples
        simple_log_example()
        structured_logging_with_spans()
        batch_logging_example()
        advanced_logging_example()
        
        print("\n" + "=" * 40)
        print("All logging examples completed successfully!")
        print("Check your Braintrust dashboard to see the logged data.")
        
    except Exception as e:
        print(f"Error running examples: {e}")
        print("Make sure you have a valid BRAINTRUST_API_KEY and internet connection.")


if __name__ == "__main__":
    main()
